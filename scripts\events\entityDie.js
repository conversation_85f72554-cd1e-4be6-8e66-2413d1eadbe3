import { world, BlockPermutation, ItemStack } from "@minecraft/server";
import { apiSafeArea } from "../lib/block/safeArea";
import { apiEquippable } from "../lib/player/equippable";
import { apiInventory } from "../lib/player/inventory";
import { apiVec3 } from "../lib/math/vector";
import { apiXp } from "../lib/player/xp";
import { apiWarn } from "../lib/player/warn";
import { apiItemDynamic } from "../lib/item/dynamic";
import { gravestoneIds } from "../variables";

const dimensionIds = {
    "minecraft:overworld": "",
    "minecraft:nether": "_nether",
    "minecraft:the_end": "_end"
};

const dimensionSoil = {
    "minecraft:overworld": "minecraft:dirt",
    "minecraft:nether": "minecraft:netherrack",
    "minecraft:the_end": "minecraft:end_stone"
};

world.afterEvents.entityDie.subscribe(({ deadEntity: entity, damageSource: source }) => {
    if (entity.typeId !== "minecraft:player") return;

    const player = entity;
    const playerEquip = player.getComponent("equippable");
    const playerInv = player.getComponent("inventory")?.container;
    if (!playerEquip || !playerInv) return;

    if (apiInventory.getItems(playerInv).length < 1 && apiEquippable.getItems(playerEquip).length < 1) return;

    // Remove any existing ghost entities for this player when they die
    player.dimension.getEntities({ type: "gravestone_death_path:ghost", tags: [`gravestone_death_path.owner: ${player.id}`] }).forEach(entity => entity.remove());

    const pos = apiSafeArea.getSafePos(player.location, player.dimension);
    const blockBelow = player.dimension.getBlock(apiVec3.offset(pos, apiVec3.offsetDirection["Down"]));

    const degrees = player.getRotation().y + 180;
    const dir = apiVec3.directions4[
        degrees < 45 || degrees > 315 ? 2 :
        degrees > 45 && degrees < 135 ? 3 :
        degrees > 135 && degrees < 225 ? 0 : 1
    ];

    const baseId = `gravestone_death_path:gravestone${dimensionIds[player.dimension.id] ?? ""}`;
    const graveId = `${baseId}_${source?.damagingEntity?.typeId?.replace("minecraft:", "") ?? ""}`;

    player.dimension.setBlockPermutation(pos, BlockPermutation.resolve(
        gravestoneIds.includes(graveId) ? graveId : baseId,
        { "minecraft:cardinal_direction": dir.toLowerCase() }
    ));

    if (blockBelow) {
        blockBelow.setType(dimensionSoil[player.dimension.id] ?? "minecraft:dirt");
    }

    const graveE = player.dimension.spawnEntity(
        "gravestone_death_path:gravestone_entity",
        apiVec3.offset(apiVec3.bottomCenter(pos), apiVec3.divide(apiVec3.offsetDirection["Up"], 10))
    );
    graveE.nameTag = `Grave`;
    graveE.setDynamicProperty("ownerXUID", player.id);
    graveE.setDynamicProperty("ownerName", player.name);

    const graveInv = graveE.getComponent("inventory")?.container;
    if (!graveInv) return;

    player.setDynamicProperty("gravestone_death_path:death_path_count", undefined);
    apiEquippable.transferItemsToGravestone(playerEquip, graveInv);
    apiInventory.transferItemsToGravestone(playerInv, graveInv);
    apiXp.transferXpToGravestone(graveE, player);

    const keyItem = new ItemStack("gravestone_death_path:gravestone_key", 1);
    const lore = [`Grave Key for ${player.name}`, `Grave ID: ${graveE.id}`, `x: ${pos.x} y: ${pos.y} z: ${pos.z}`];
    keyItem.setLore(lore);
    // Store owner information in the key for validation
    keyItem.setDynamicProperty("gravestone_death_path:death_info", player.id);
    keyItem.setDynamicProperty("gravestone_death_path:grave_entity_id", graveE.id);
    // Transfer death path to the key for ghost navigation
    apiItemDynamic.transferDeathPath(player, keyItem);
    playerInv.addItem(keyItem);

    apiWarn.notify(player, {
        rawtext: [
            { translate: "dimension.gravestone_death_path:die1", with: [`§cx: ${pos.x}, §by: ${pos.y}, §az: ${pos.z}§r`] },
            { translate: `dimension.gravestone_death_path:${player.dimension.id.replace("minecraft:", "")}` },
            { translate: "dimension.gravestone_death_path:die2" }
        ]
    });
});