import { collectGravestone } from "../functions/collect";
import { world, system } from "@minecraft/server";
import { gravestoneIds } from "../variables";
import { apiWarn } from "../lib/player/warn";
import { apiVec3 } from "../lib/math/vector";

world.beforeEvents.playerBreakBlock.subscribe((eventData) => {
    const { block, player } = eventData;

    // Check if player has the correct key in their inventory
    const playerInv = player.getComponent("inventory")?.container;
    if (!playerInv) {
        eventData.cancel = true;
        apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });
        return;
    }

    let hasValidKey = false;
    let validKeyItem = null;

    // Check all items in inventory for a valid key
    for (let i = 0; i < playerInv.size; i++) {
        const item = playerInv.getItem(i);
        if (item && item.typeId === "gravestone_death_path:gravestone_key") {
            const keyOwner = item.getDynamicProperty("gravestone_death_path:death_info");
            const posKey = item.getLore()[2]; // Get position from lore

            if (keyOwner === player.id && posKey) {
                // Parse position from lore format "x: # y: # z: #"
                const coords = posKey.split(" ");
                if (coords.length >= 6) {
                    const keyPos = {
                        x: parseInt(coords[1]),
                        y: parseInt(coords[3]),
                        z: parseInt(coords[5])
                    };

                    if (apiVec3.compare(block.location, keyPos)) {
                        hasValidKey = true;
                        validKeyItem = item;
                        break;
                    }
                }
            }
        }
    }

    if (!hasValidKey) {
        eventData.cancel = true;
        apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });
        return;
    }

    // Additional validation by checking the grave entity ownership
    const graveE = block.dimension.getEntities({
        type: "gravestone_death_path:gravestone_entity",
        location: apiVec3.bottomCenter(block.location),
        maxDistance: 1,
        closest: 1
    })[0];

    if (graveE) {
        const graveOwner = graveE.getDynamicProperty("ownerXUID");
        const keyGraveId = validKeyItem.getDynamicProperty("gravestone_death_path:grave_entity_id");

        if (graveOwner !== player.id || keyGraveId !== graveE.id) {
            eventData.cancel = true;
            apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });
            return;
        }
    }

    // If we get here, the player has a valid key - allow the break and collect items
    system.run(() => {
        const success = collectGravestone(block, player, false, validKeyItem);
        if (!success) {
            apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });
        }
    });
}, { blockTypes: gravestoneIds });
