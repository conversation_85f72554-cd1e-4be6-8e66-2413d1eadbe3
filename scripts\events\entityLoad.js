import { apiInventory } from "../lib/player/inventory";
import { world, system } from "@minecraft/server";

// Only target specific addon entities that should be cleaned up
const ENTITIES_TO_CLEANUP = [
    "gravestone_death_path:ghost"
    // Add other addon-specific entities here if needed
];

world.afterEvents.entityLoad.subscribe(({ entity }) => {
    if (!entity?.isValid()) {
        return;
    }

    // Only process addon-specific entities that need cleanup
    if (ENTITIES_TO_CLEANUP.includes(entity.typeId)) {
        const block = entity.dimension.getBlock(entity.location);
        if (block == undefined || block.getTags().includes("gravestone_death_path:gravestone"))
            return;
        apiInventory.dropInventory(entity.id);
        system.runTimeout(() => { world.getEntity(entity.id)?.remove(); }, 10);
    }
});

