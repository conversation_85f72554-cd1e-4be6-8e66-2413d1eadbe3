import { collectGravestone } from "../functions/collect";
import { apiItemDynamic } from "../lib/item/dynamic";
import { world } from "@minecraft/server";
import { deathPath } from "../functions/locator";
import { apiTimer } from "../lib/player/timer";
import { apiVec3 } from "../lib/math/vector";
import { apiWarn } from "../lib/player/warn";
import { apiLore } from "../lib/item/lore";
world.beforeEvents.worldInitialize.subscribe(({ itemComponentRegistry: customI }) => {
    customI.registerCustomComponent("gravestone_death_path:gravestone_key", {
        onUseOn({ source, itemStack: item, block }) {
            const player = source;
            if (block.getTags().includes("gravestone_death_path:gravestone")) {
                const posKey = apiLore.getDeathPos(item);
                if (!posKey)
                    return;
                if (!apiVec3.compare(block.location, posKey))
                    return apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_gravestone", { sound: "gravestone_death_path.warn.bass" });

                // Validate key ownership before allowing grave access
                const keyOwner = item.getDynamicProperty("gravestone_death_path:death_info");
                if (keyOwner !== player.id)
                    return apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });

                player.dimension.getEntities({ type: "gravestone_death_path:ghost", tags: [`gravestone_death_path.owner: ${player.id}`] }).forEach(entity => entity.remove());
                const success = collectGravestone(block, player, true, item);
                if (!success) {
                    return apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });
                }
                // Key is automatically consumed by collectGravestone when removeKey=true
                return;
            }
            if (!apiTimer.checkCooldown(player, "gravestone_death_path:gravestone_key_cooldown").finished && !player.hasTag("dev"))
                return;
            apiTimer.setCooldown(player, "gravestone_death_path:gravestone_key_cooldown", 5);
            const keyOwner = apiItemDynamic.getDeathInfo(item);
            if (keyOwner == undefined || keyOwner != player.id)
                return apiWarn.notify(player, "warning.gravestone_death_path:gravestone_key.invalid_owner", { sound: "gravestone_death_path.warn.bass" });
            deathPath.start(player, item, block);
        }
    });
});
